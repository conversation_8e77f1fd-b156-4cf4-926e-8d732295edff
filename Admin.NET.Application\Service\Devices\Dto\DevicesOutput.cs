// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Magicodes.ExporterAndImporter.Core;
namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备输出参数
/// </summary>
public class DevicesOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 网关ID
    /// </summary>
    public long? ap_id { get; set; }    

    /// <summary>
    /// 网关mac
    /// </summary>
    public string apMac { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public string device_id { get; set; }    
    
    /// <summary>
    /// 设备名称
    /// </summary>
    public string? device_name { get; set; }    
    
    /// <summary>
    /// MAC地址
    /// </summary>
    public string? mac_address { get; set; }    
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }    
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }    
    
    /// <summary>
    /// 电量
    /// </summary>
    public int? battery_level { get; set; }

    /// <summary>
    /// 信号强度
    /// </summary>
    public int? signal_strength { get; set; }

    /// <summary>
    /// 固件版本
    /// </summary>
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public long? BindTenantId { get; set; }    
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public long? BindUserId { get; set; } 
    /// <summary>
    /// 工位号
    /// </summary>
    public int staffCode { get; set; }
    /// <summary>
    /// 会议室ID
    /// </summary>
    public long? roomId { get; set; }
    /// <summary>
    /// 模板ID
    /// </summary>
    public long? templateId { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 设备绑定信息
    /// </summary>
    public DeviceBindingInfo? BindingInfo { get; set; }

}

/// <summary>
/// 设备绑定信息
/// </summary>
public class DeviceBindingInfo
{
    /// <summary>
    /// 绑定ID
    /// </summary>
    public long? BindingId { get; set; }
    
    /// <summary>
    /// 员工ID
    /// </summary>
    public long? StaffId { get; set; }
    
    /// <summary>
    /// 模板ID
    /// </summary>
    public long? TemplateId { get; set; }
    
    /// <summary>
    /// 会议室ID
    /// </summary>
    public long? MeetingRoomId { get; set; }
    
    /// <summary>
    /// 绑定类型：1、员工；2、房间；3：模版
    /// </summary>
    public int? BindingType { get; set; }
    
    /// <summary>
    /// 绑定类型名称
    /// </summary>
    public string? BindingTypeName { get; set; }
    
    /// <summary>
    /// 绑定数据
    /// </summary>
    public object? BindingData { get; set; }
    
    /// <summary>
    /// 绑定创建时间
    /// </summary>
    public DateTime? BindingCreateTime { get; set; }
}

/// <summary>
/// 设备统计输出参数
/// </summary>
public class DeviceStatisticsOutput
{
    /// <summary>
    /// 设备总数
    /// </summary>
    public int TotalDevices { get; set; }

    /// <summary>
    /// 在线设备数量
    /// </summary>
    public int OnlineDevices { get; set; }

    /// <summary>
    /// 离线设备数量
    /// </summary>
    public int OfflineDevices { get; set; }

    /// <summary>
    /// 低电量设备数量（电量低于20%）
    /// </summary>
    public int LowBatteryDevices { get; set; }

    /// <summary>
    /// 弱信号设备数量（信号弱于-80dBm）
    /// </summary>
    public int WeakSignalDevices { get; set; }

    /// <summary>
    /// 在线率（百分比）
    /// </summary>
    public decimal OnlineRate { get; set; }

    /// <summary>
    /// 低电量设备占比（百分比）
    /// </summary>
    public decimal LowBatteryRate { get; set; }

    /// <summary>
    /// 弱信号设备占比（百分比）
    /// </summary>
    public decimal WeakSignalRate { get; set; }
}

/// <summary>
/// 设备详细统计输出参数
/// </summary>
public class DeviceDetailedStatisticsOutput
{
    /// <summary>
    /// 基础统计信息
    /// </summary>
    public DeviceStatisticsOutput BasicStatistics { get; set; }

    /// <summary>
    /// 按设备类型分组的统计
    /// </summary>
    public List<DeviceTypeStatistics> DeviceTypeStatistics { get; set; }

    /// <summary>
    /// 电量分布统计
    /// </summary>
    public BatteryDistributionStatistics BatteryDistribution { get; set; }

    /// <summary>
    /// 信号强度分布统计
    /// </summary>
    public SignalDistributionStatistics SignalDistribution { get; set; }
}

/// <summary>
/// 按设备类型统计
/// </summary>
public class DeviceTypeStatistics
{
    /// <summary>
    /// 设备类型（1：姓名桌牌；2：价格标签）
    /// </summary>
    public int DeviceType { get; set; }

    /// <summary>
    /// 设备类型名称
    /// </summary>
    public string DeviceTypeName { get; set; }

    /// <summary>
    /// 该类型设备总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 该类型在线设备数
    /// </summary>
    public int OnlineCount { get; set; }

    /// <summary>
    /// 该类型离线设备数
    /// </summary>
    public int OfflineCount { get; set; }

    /// <summary>
    /// 该类型低电量设备数
    /// </summary>
    public int LowBatteryCount { get; set; }

    /// <summary>
    /// 该类型弱信号设备数
    /// </summary>
    public int WeakSignalCount { get; set; }
}

/// <summary>
/// 电量分布统计
/// </summary>
public class BatteryDistributionStatistics
{
    /// <summary>
    /// 电量0-20%的设备数量
    /// </summary>
    public int Battery0To20 { get; set; }

    /// <summary>
    /// 电量21-50%的设备数量
    /// </summary>
    public int Battery21To50 { get; set; }

    /// <summary>
    /// 电量51-80%的设备数量
    /// </summary>
    public int Battery51To80 { get; set; }

    /// <summary>
    /// 电量81-100%的设备数量
    /// </summary>
    public int Battery81To100 { get; set; }

    /// <summary>
    /// 平均电量
    /// </summary>
    public decimal AverageBattery { get; set; }
}

/// <summary>
/// 信号强度分布统计
/// </summary>
public class SignalDistributionStatistics
{
    /// <summary>
    /// 信号强度小于-80dBm的设备数量（弱信号）
    /// </summary>
    public int SignalBelow80 { get; set; }

    /// <summary>
    /// 信号强度-80到-60dBm的设备数量
    /// </summary>
    public int Signal80To60 { get; set; }

    /// <summary>
    /// 信号强度-60到-40dBm的设备数量
    /// </summary>
    public int Signal60To40 { get; set; }

    /// <summary>
    /// 信号强度大于-40dBm的设备数量（强信号）
    /// </summary>
    public int SignalAbove40 { get; set; }

    /// <summary>
    /// 平均信号强度
    /// </summary>
    public decimal AverageSignal { get; set; }
}

/// <summary>
/// 设备统计查询输入参数
/// </summary>
public class DeviceStatisticsInput
{
    /// <summary>
    /// 开始时间（可选，用于查询指定时间范围内的设备）
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间（可选，用于查询指定时间范围内的设备）
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 设备类型过滤（可选，1：姓名桌牌；2：价格标签）
    /// </summary>
    public int? DeviceType { get; set; }

    /// <summary>
    /// 网关ID过滤（可选）
    /// </summary>
    public long? ApId { get; set; }

    /// <summary>
    /// 会议室ID过滤（可选，0表示所有会议室）
    /// </summary>
    public long? MeetingRoomId { get; set; }
}

/// <summary>
/// 按会议室分组的设备统计
/// </summary>
public class MeetingRoomDeviceStatistics
{
    /// <summary>
    /// 会议室ID（0表示所有会议室）
    /// </summary>
    public long MeetingRoomId { get; set; }

    /// <summary>
    /// 会议室名称
    /// </summary>
    public string MeetingRoomName { get; set; }

    /// <summary>
    /// 该会议室绑定的设备总数
    /// </summary>
    public int TotalDevices { get; set; }

    /// <summary>
    /// 该会议室在线设备数量
    /// </summary>
    public int OnlineDevices { get; set; }

    /// <summary>
    /// 该会议室离线设备数量
    /// </summary>
    public int OfflineDevices { get; set; }

    /// <summary>
    /// 该会议室低电量设备数量
    /// </summary>
    public int LowBatteryDevices { get; set; }

    /// <summary>
    /// 该会议室弱信号设备数量
    /// </summary>
    public int WeakSignalDevices { get; set; }

    /// <summary>
    /// 该会议室设备在线率
    /// </summary>
    public decimal OnlineRate { get; set; }
}
