// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.Cache;
using Admin.NET.Core.Service;
using Admin.NET.Plugin.GreenDisplay.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 接入点/网关表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class AccessPointsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<AccessPoints> _accessPointsRep;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<AccessPointsService> _logger;
    private readonly APDeviceCacheService _cacheService;

    public AccessPointsService(SqlSugarRepository<AccessPoints> accessPointsRep, GreenDisplayService greenDisplayService, ILogger<AccessPointsService> logger, APDeviceCacheService cacheService)
    {
        _accessPointsRep = accessPointsRep;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 分页查询接入点/网关表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询接入点/网关表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<AccessPointsOutput>> Page(PageAccessPointsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _accessPointsRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.ap_name.Contains(input.Keyword) || u.ap_location.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ap_name), u => u.ap_name.Contains(input.ap_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ap_location), u => u.ap_location.Contains(input.ap_location.Trim()))
            .WhereIF(input.ap_status != null, u => u.ap_status == input.ap_status)
            .Select<AccessPointsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取接入点/网关表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取接入点/网关表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AccessPoints> Detail([FromQuery] QueryByIdAccessPointsInput input)
    {
        return await _accessPointsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加接入点/网关表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加接入点/网关表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddAccessPointsInput input)
    {
        var entity = input.Adapt<AccessPoints>();
        
        _logger.LogInformation("开始创建接入点，MAC地址: {MacAddress}, 名称: {ApName}, 位置: {ApLocation}", 
            entity.mac_address, entity.ap_name, entity.ap_location);
        
        // 检验MAC地址唯一性
        var existingAP = await _accessPointsRep.GetFirstAsync(u => u.mac_address == entity.mac_address);
        if (existingAP != null)
        {
            _logger.LogWarning("MAC地址重复，创建失败，MAC地址: {MacAddress}", entity.mac_address);
            throw Oops.Oh("MAC地址已存在，不允许重复");
        }
        
        CreateAPInput aPInput = new CreateAPInput()
        {
            ApName = entity.ap_name,
            ApMac = entity.mac_address,
            Description = string.Empty
        };
        
        // 使用事务确保数据一致性，设置超时时间为30秒
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        
        var result = await _accessPointsRep.AsTenant().UseTranAsync(async () =>
        {
            // 先尝试在第三方平台创建AP，带重试机制
            bool device = await CreateAPWithRetryAsync(aPInput, cts.Token);
            if (!device)
            {
                _logger.LogError("第三方平台创建AP失败，MAC地址: {MacAddress}", entity.mac_address);
                throw Oops.Oh("第三方平台创建AP失败");
            }
            
            _logger.LogInformation("第三方平台创建AP成功，MAC地址: {MacAddress}", entity.mac_address);
            
            // 第三方平台创建成功后，再在本地数据库中插入记录
            bool insertResult = await _accessPointsRep.InsertAsync(entity);
            if (!insertResult)
            {
                _logger.LogError("本地数据库插入失败，MAC地址: {MacAddress}", entity.mac_address);
                throw Oops.Oh("本地数据库插入失败");
            }
            
            _logger.LogInformation("本地数据库插入成功，ID: {Id}, MAC地址: {MacAddress}", entity.Id, entity.mac_address);
            
            return entity.Id;
        });
        
        _logger.LogInformation("接入点创建完成，ID: {Id}, MAC地址: {MacAddress}", result.Data, entity.mac_address);

        // 清除AP缓存，确保下次查询时获取最新数据
        _cacheService.ClearAllAPCache();

        return result.Data;
    }
    
    /// <summary>
    /// 带重试机制的创建AP方法
    /// </summary>
    /// <param name="aPInput">创建AP输入参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task<bool> CreateAPWithRetryAsync(CreateAPInput aPInput, CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int baseDelayMs = 1000;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                _logger.LogInformation("尝试创建第三方平台AP，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, aPInput.ApMac);
                
                bool result = await _greenDisplayService.CreateAPAsync(aPInput);
                
                if (result)
                {
                    _logger.LogInformation("第三方平台创建AP成功，尝试次数: {Attempt}, MAC地址: {MacAddress}", 
                        attempt, aPInput.ApMac);
                    return true;
                }
                
                _logger.LogWarning("第三方平台创建AP失败，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, aPInput.ApMac);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("创建AP操作超时，MAC地址: {MacAddress}", aPInput.ApMac);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建AP时发生异常，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, aPInput.ApMac);
                
                if (attempt == maxRetries)
                {
                    throw;
                }
            }
            
            if (attempt < maxRetries)
            {
                int delay = baseDelayMs * (int)Math.Pow(2, attempt - 1); // 指数退避
                _logger.LogInformation("等待 {Delay}ms 后重试创建AP，MAC地址: {MacAddress}", delay, aPInput.ApMac);
                await Task.Delay(delay, cancellationToken);
            }
        }
        
        return false;
    }

    /// <summary>
    /// 更新接入点/网关表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新接入点/网关表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateAccessPointsInput input)
    {
        var entity = input.Adapt<AccessPoints>();
        
        _logger.LogInformation("开始更新接入点，ID: {Id}, MAC地址: {MacAddress}, 名称: {ApName}", 
            entity.Id, entity.mac_address, entity.ap_name);
        
        // 获取原始数据用于比较
        var originalEntity = await _accessPointsRep.GetFirstAsync(u => u.Id == entity.Id) 
            ?? throw Oops.Oh("接入点不存在");
        
        // 构建第三方平台更新参数，使用更新后的数据
        UpdateAPInput updateAPInput = new UpdateAPInput()
        {
            ApMac = entity.mac_address ?? originalEntity.mac_address,
            ApName = entity.ap_name ?? originalEntity.ap_name,
            Status = entity.ap_status?.ToString(),
            Version = entity.firmware_version,
            ApIp = entity.ip_address,
            Description = entity.ap_location
        };
        _logger.LogInformation("原始接入点信息 - ID: {Id}, MAC地址: {MacAddress}, 名称: {ApName}, 位置: {ApLocation}", 
            originalEntity.Id, originalEntity.mac_address, originalEntity.ap_name, originalEntity.ap_location);
        
        // 使用事务确保数据一致性，设置超时时间为30秒
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        
        await _accessPointsRep.AsTenant().UseTranAsync(async () =>
        {
            try
            {
                cts.Token.ThrowIfCancellationRequested();
                
                // 更新本地数据库
                int updateResult = await _accessPointsRep.AsUpdateable(entity).ExecuteCommandAsync();
                if (updateResult <= 0)
                {
                    _logger.LogError("本地数据库更新失败，ID: {Id}", entity.Id);
                    throw Oops.Oh("本地数据库更新失败");
                }
                
                _logger.LogInformation("本地数据库更新成功，ID: {Id}, 影响行数: {RowsAffected}", 
                    entity.Id, updateResult);
                
                // 同步更新第三方平台数据
                _logger.LogInformation("开始同步更新第三方平台，MAC地址: {MacAddress}", updateAPInput.ApMac);
                
                var thirdPartyUpdateResult = await _greenDisplayService.UpdateAPAsync(updateAPInput);
                if (!thirdPartyUpdateResult)
                {
                    _logger.LogError("第三方平台更新失败，MAC地址: {MacAddress}", updateAPInput.ApMac);
                    throw Oops.Oh("第三方平台更新失败");
                } 
                _logger.LogInformation("第三方平台更新成功，MAC地址: {MacAddress}", updateAPInput.ApMac); 
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("更新接入点操作超时，ID: {Id}", entity.Id);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新接入点时发生异常，ID: {Id}", entity.Id);
                throw;
            }
        });
        
        _logger.LogInformation("接入点更新完成，ID: {Id}, MAC地址: {MacAddress}",
            entity.Id, entity.mac_address);

        // 清除AP缓存，确保下次查询时获取最新数据
        _cacheService.ClearAllAPCache();
    }

    /// <summary>
    /// 删除接入点/网关表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除接入点/网关表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAccessPointsInput input)
    {
        var entity = await _accessPointsRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        
        _logger.LogInformation("开始删除接入点，ID: {Id}, MAC地址: {MacAddress}, 名称: {ApName}", 
            entity.Id, entity.mac_address, entity.ap_name);
        
        // 使用事务确保数据一致性，设置超时时间为30秒
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        
        await _accessPointsRep.AsTenant().UseTranAsync(async () =>
        {
            // 先尝试在第三方平台删除AP，带重试机制
            bool device = await DeleteAPWithRetryAsync(entity.mac_address, cts.Token);
            if (!device)
            {
                _logger.LogError("第三方平台删除AP失败，MAC地址: {MacAddress}", entity.mac_address);
                throw Oops.Oh("第三方平台删除AP失败");
            }
            
            _logger.LogInformation("第三方平台删除AP成功，MAC地址: {MacAddress}", entity.mac_address);
            
            // 第三方平台删除成功后，再在本地数据库中删除记录
            int deleteResult = await _accessPointsRep.FakeDeleteAsync(entity);
            if (deleteResult <= 0)
            {
                _logger.LogError("本地数据库删除失败，ID: {Id}", entity.Id);
                throw Oops.Oh("本地数据库删除失败");
            }
            
            _logger.LogInformation("本地数据库删除成功，ID: {Id}", entity.Id);
        });
        
        _logger.LogInformation("接入点删除完成，ID: {Id}, MAC地址: {MacAddress}",
            entity.Id, entity.mac_address);

        // 清除AP缓存，确保下次查询时获取最新数据
        _cacheService.ClearAllAPCache();
    }
    
    /// <summary>
    /// 带重试机制的删除AP方法
    /// </summary>
    /// <param name="macAddress">MAC地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task<bool> DeleteAPWithRetryAsync(string macAddress, CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int baseDelayMs = 1000;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                _logger.LogInformation("尝试删除第三方平台AP，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, macAddress);
                
                bool result = await _greenDisplayService.DeleteAPAsync(macAddress);
                
                if (result)
                {
                    _logger.LogInformation("第三方平台删除AP成功，尝试次数: {Attempt}, MAC地址: {MacAddress}", 
                        attempt, macAddress);
                    return true;
                }
                
                _logger.LogWarning("第三方平台删除AP失败，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, macAddress);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("删除AP操作超时，MAC地址: {MacAddress}", macAddress);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除AP时发生异常，第 {Attempt}/{MaxRetries} 次，MAC地址: {MacAddress}", 
                    attempt, maxRetries, macAddress);
                
                if (attempt == maxRetries)
                {
                    throw;
                }
            }
            
            if (attempt < maxRetries)
            {
                int delay = baseDelayMs * (int)Math.Pow(2, attempt - 1); // 指数退避
                _logger.LogInformation("等待 {Delay}ms 后重试删除AP，MAC地址: {MacAddress}", delay, macAddress);
                await Task.Delay(delay, cancellationToken);
            }
        }
        
        return false;
    }

    /// <summary>
    /// 批量删除接入点/网关表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除接入点/网关表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteAccessPointsInput> input)
    {
        _logger.LogInformation("开始批量删除接入点，数量: {Count}", input.Count);
        
        var exp = Expressionable.Create<AccessPoints>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _accessPointsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
        
        if (list.Count == 0)
        {
            _logger.LogWarning("未找到要删除的接入点");
            return 0;
        }
        
        _logger.LogInformation("找到 {Count} 个接入点需要删除", list.Count);
        
        // 使用事务确保数据一致性，设置超时时间为60秒（批量操作需要更长时间）
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
        
        int deletedCount = 0;
        
        await _accessPointsRep.AsTenant().UseTranAsync(async () =>
        {
            // 先尝试在第三方平台批量删除AP
            var failedDeletions = new List<string>();
            
            foreach (var entity in list)
            {
                try
                {
                    bool device = await DeleteAPWithRetryAsync(entity.mac_address, cts.Token);
                    if (!device)
                    {
                        _logger.LogError("第三方平台删除AP失败，MAC地址: {MacAddress}", entity.mac_address);
                        failedDeletions.Add(entity.mac_address);
                    }
                    else
                    {
                        _logger.LogInformation("第三方平台删除AP成功，MAC地址: {MacAddress}", entity.mac_address);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "删除第三方平台AP时发生异常，MAC地址: {MacAddress}", entity.mac_address);
                    failedDeletions.Add(entity.mac_address);
                }
            }
            
            if (failedDeletions.Count > 0)
            {
                _logger.LogError("部分第三方平台AP删除失败，失败数量: {FailedCount}, 失败MAC地址: {FailedMacs}", 
                    failedDeletions.Count, string.Join(", ", failedDeletions));
                throw Oops.Oh($"第三方平台删除失败，失败数量: {failedDeletions.Count}");
            }
            
            _logger.LogInformation("所有第三方平台AP删除成功");
            
            // 第三方平台删除成功后，再在本地数据库中批量删除记录
            deletedCount = await _accessPointsRep.FakeDeleteAsync(list);   //假删除
            //deletedCount = await _accessPointsRep.DeleteAsync(list);   //真删除
            
            if (deletedCount <= 0)
            {
                _logger.LogError("本地数据库批量删除失败");
                throw Oops.Oh("本地数据库批量删除失败");
            }
            
            _logger.LogInformation("本地数据库批量删除成功，删除数量: {DeletedCount}", deletedCount);
        });
        
        _logger.LogInformation("批量删除接入点完成，成功删除数量: {DeletedCount}", deletedCount);

        // 清除AP缓存，确保下次查询时获取最新数据
        _cacheService.ClearAllAPCache();

        return deletedCount;
    }

    /// <summary>
    /// AP网关命令下发
    /// </summary>
    /// <param name="macAddress">网关MAC地址</param>
    /// <param name="pcommanda">下发的命令</param>
    /// <returns>命令下发是否成功</returns>
    [DisplayName("AP网关命令下发")]
    [ApiDescriptionSettings(Name = "command"), HttpPost]
    public async Task<bool> command(UpdateAPCommandInput updateAPCommandInput)
    {
        try
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(updateAPCommandInput.apMac))
            {
                _logger.LogWarning("AP网关命令下发失败：MAC地址不能为空");
                throw Oops.Oh("MAC地址不能为空");
            }
            
            if (string.IsNullOrWhiteSpace(updateAPCommandInput.command))
            {
                _logger.LogWarning("AP网关命令下发失败：命令内容不能为空，MAC地址: {MacAddress}",   updateAPCommandInput.apMac);
                throw Oops.Oh("命令内容不能为空");
            }
            
            // 检查AP是否存在于本地数据库
            var existingAP = await _accessPointsRep.GetFirstAsync(u => u.mac_address == updateAPCommandInput.apMac);
            if (existingAP == null)
            {
                _logger.LogWarning("AP网关命令下发失败：未找到指定的AP设备，MAC地址: {MacAddress}", updateAPCommandInput.apMac);
                throw Oops.Oh($"未找到MAC地址为 {updateAPCommandInput.apMac} 的AP设备");
            }
            
            _logger.LogInformation("开始向AP网关下发命令，MAC地址: {MacAddress}, 命令: {Command}",  updateAPCommandInput.apMac, updateAPCommandInput.command);
            
             
            
            // 调用第三方平台API下发命令
            bool result = await _greenDisplayService.CommandAPAsync(updateAPCommandInput);
            
            if (result)
            {
                _logger.LogInformation("AP网关命令下发成功，MAC地址: {MacAddress}, 命令: {Command}", updateAPCommandInput.apMac, updateAPCommandInput.command);
            }
            else
            {
                _logger.LogWarning("AP网关命令下发失败，MAC地址: {MacAddress}, 命令: {Command}", updateAPCommandInput.apMac, updateAPCommandInput.command);
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AP网关命令下发时发生异常，MAC地址: {MacAddress}, 命令: {Command}", updateAPCommandInput.apMac, updateAPCommandInput.command);
            throw;
        }
    }

    #region  第三方平台批量同步
    /// <summary>
    /// 第三方平台批量同步接入点数据 🔄
    /// </summary>
    /// <returns>同步结果</returns>
    [DisplayName("第三方平台批量同步接入点数据")]
    [ApiDescriptionSettings(Name = "SyncFromThirdParty"), HttpPost]
    public async Task<SyncResult> SyncFromThirdParty()
    {
        _logger.LogInformation("开始从第三方平台批量同步接入点数据");
        
        var result = new SyncResult();
        
        try
        {
            // 设置超时时间为60秒
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
            
            // 从第三方平台获取所有AP数据
            var thirdPartyAPs = await GetAllThirdPartyAPsAsync(cts.Token);
            
            if (thirdPartyAPs == null || thirdPartyAPs.Count == 0)
            {
                _logger.LogWarning("第三方平台未返回任何AP数据");
                result.Message = "第三方平台未返回任何AP数据";
                return result;
            }
            
            _logger.LogInformation("从第三方平台获取到 {Count} 个AP数据", thirdPartyAPs.Count);
            
            // 获取本地所有AP数据
            var localAPs = await _accessPointsRep.AsQueryable().ToListAsync();
            var localAPDict = localAPs.ToDictionary(ap => ap.mac_address, ap => ap);
            
            _logger.LogInformation("本地数据库中有 {Count} 个AP数据", localAPs.Count);
            
            // 使用事务确保数据一致性
            await _accessPointsRep.AsTenant().UseTranAsync(async () =>
            {
                foreach (var thirdPartyAP in thirdPartyAPs)
                {
                    try
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        
                        if (string.IsNullOrEmpty(thirdPartyAP.ApMac))
                        {
                            _logger.LogWarning("跳过MAC地址为空的AP数据");
                            result.SkippedCount++;
                            continue;
                        }
                        
                        // 检查本地是否已存在该AP
                        if (localAPDict.TryGetValue(thirdPartyAP.ApMac, out var existingAP))
                        {
                            // 更新现有AP
                            bool updated = await UpdateExistingAPAsync(existingAP, thirdPartyAP);
                            if (updated)
                            {
                                result.UpdatedCount++;
                                _logger.LogDebug("更新AP成功，MAC: {Mac}", thirdPartyAP.ApMac);
                            }
                            else
                            {
                                result.SkippedCount++;
                                _logger.LogDebug("AP数据无变化，跳过更新，MAC: {Mac}", thirdPartyAP.ApMac);
                            }
                        }
                        else
                        {
                            // 创建新AP
                            var newAP = MapToAccessPoints(thirdPartyAP);
                            await _accessPointsRep.InsertAsync(newAP);
                            result.CreatedCount++;
                            _logger.LogDebug("创建新AP成功，MAC: {Mac}", thirdPartyAP.ApMac);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogWarning("同步操作超时");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理AP数据时发生异常，MAC: {Mac}", thirdPartyAP.ApMac);
                        result.ErrorCount++;
                    }
                }
            });
            
            result.Success = true;
            result.Message = $"同步完成：新增 {result.CreatedCount} 个，更新 {result.UpdatedCount} 个，跳过 {result.SkippedCount} 个，错误 {result.ErrorCount} 个";
            
            _logger.LogInformation("第三方平台批量同步完成：{Message}", result.Message);
        }
        catch (OperationCanceledException)
        {
            result.Message = "同步操作超时";
            _logger.LogError("第三方平台批量同步超时");
        }
        catch (Exception ex)
        {
            result.Message = $"同步失败：{ex.Message}";
            _logger.LogError(ex, "第三方平台批量同步时发生异常");
        }
        
        return result;
    }

    /// <summary>
    /// 获取第三方平台所有AP数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>AP数据列表</returns>
    private async Task<List<APOutput>> GetAllThirdPartyAPsAsync(CancellationToken cancellationToken)
    {
        var allAPs = new List<APOutput>();
        int pageNo = 1;
        const int pageSize = 50; // 每页获取50条数据
        
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var queryInput = new QueryAPInput
                {
                    PageNo = pageNo,
                    PageSize = pageSize
                };
                
                var response = await _greenDisplayService.GetAPListAsync(queryInput);
                
                if (response?.list == null || response.list.Count == 0)
                {
                    _logger.LogInformation("第 {PageNo} 页无数据，结束获取", pageNo);
                    break;
                }
                
                allAPs.AddRange(response.list);
                
                _logger.LogDebug("获取第 {PageNo} 页数据成功，本页 {PageCount} 条，总计 {TotalCount} 条", 
                    pageNo, response.list.Count, allAPs.Count);
                
                // 如果当前页数据少于页大小，说明已经是最后一页
                if (response.list.Count < pageSize)
                {
                    break;
                }
                
                pageNo++;
                
                // 添加延迟避免频繁请求
                await Task.Delay(100, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取第三方平台AP数据时发生异常，页码: {PageNo}", pageNo);
                throw;
            }
        }
        
        return allAPs;
    }
    
    /// <summary>
    /// 更新现有AP数据
    /// </summary>
    /// <param name="existingAP">现有AP</param>
    /// <param name="thirdPartyAP">第三方平台AP数据</param>
    /// <returns>是否有更新</returns>
    private async Task<bool> UpdateExistingAPAsync(AccessPoints existingAP, Admin.NET.Plugin.GreenDisplay.Service.APOutput thirdPartyAP)
    {
        bool hasChanges = false;
        
        // 检查并更新AP名称
        if (!string.IsNullOrEmpty(thirdPartyAP.ApName) && existingAP.ap_name != thirdPartyAP.ApName)
        {
            existingAP.ap_name = thirdPartyAP.ApName;
            hasChanges = true;
        }
        
        // 检查并更新IP地址
        if (!string.IsNullOrEmpty(thirdPartyAP.ApIp) && existingAP.ip_address != thirdPartyAP.ApIp)
        {
            existingAP.ip_address = thirdPartyAP.ApIp;
            hasChanges = true;
        }
        
        // 检查并更新状态
        var newStatus = thirdPartyAP.Status;
        if ( existingAP.ap_status != newStatus)
        {
            existingAP.ap_status = newStatus;
            hasChanges = true;
        }
        
        // 检查并更新固件版本
        if (!string.IsNullOrEmpty(thirdPartyAP.Version) && existingAP.firmware_version != thirdPartyAP.Version)
        {
            existingAP.firmware_version = thirdPartyAP.Version;
            hasChanges = true;
        }
        
        if (hasChanges)
        {
            await _accessPointsRep.UpdateAsync(existingAP);
        }
        
        return hasChanges;
    }
    
    /// <summary>
    /// 将第三方平台AP数据映射为本地AccessPoints实体
    /// </summary>
    /// <param name="apOutput">第三方平台AP数据</param>
    /// <returns>AccessPoints实体</returns>
    private AccessPoints MapToAccessPoints(Admin.NET.Plugin.GreenDisplay.Service.APOutput apOutput)
    {
        return new AccessPoints
        {
            ap_name = apOutput.ApName,
            mac_address = apOutput.ApMac,
            ip_address = apOutput.ApIp,
            ap_status = apOutput.Status, // 默认为离线状态
            firmware_version = apOutput.Version,
            // 其他字段使用默认值或从描述中解析
            ap_location = apOutput.Description,
            signal_strength = null, // 第三方平台暂无此数据
            connected_devices_count = 0, // 默认值
            max_devices = null // 第三方平台暂无此数据
        };
    }
    #endregion
}

