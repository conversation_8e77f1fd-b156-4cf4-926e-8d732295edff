fail: 2025-07-29 09:33:35.3003928 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:37.0499353 +08:00 Tuesday L System.Logging.ScheduleService[0] #13
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Error occurred executing in <job_onlineUser> 清理在线用户 [S] <job_onlineUser trigger_onlineUser> 1s 清理在线用户 1ts 2025-07-29 09:33:28.807 +08:00 [Overrun].      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.OnlineUserJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\OnlineUserJob.cs:line 32
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:37.0830913 +08:00 Tuesday L Admin.NET.Core.Service.JobMonitor[0] #13
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      定时任务【清理在线用户】错误：System.InvalidOperationException: Error occurred executing in <job_onlineUser> 清理在线用户 [S] <job_onlineUser trigger_onlineUser> 1s 清理在线用户 1ts 2025-07-29 09:33:28.807 +08:00 [Overrun].
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.OnlineUserJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\OnlineUserJob.cs:line 32
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
         --- End of inner exception stack trace ---
fail: 2025-07-29 09:33:39.0630298 +08:00 Tuesday L System.Logging.ScheduleService[0] #23
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Error occurred executing in <job_EnumToDictJob> 枚举转字典 [S] <job_EnumToDictJob trigger_EnumToDictJob> 1s 枚举转字典 1ts 2025-07-29 09:33:28.697 +08:00 [Overrun].      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.EnumToDictJob.SyncEnumToDictInfoAsync(SqlSugarClient db, List`1 list) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 102
         at Admin.NET.Core.EnumToDictJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 43
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:39.1046691 +08:00 Tuesday L Admin.NET.Core.Service.JobMonitor[0] #23
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      定时任务【枚举转字典】错误：System.InvalidOperationException: Error occurred executing in <job_EnumToDictJob> 枚举转字典 [S] <job_EnumToDictJob trigger_EnumToDictJob> 1s 枚举转字典 1ts 2025-07-29 09:33:28.697 +08:00 [Overrun].
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.EnumToDictJob.SyncEnumToDictInfoAsync(SqlSugarClient db, List`1 list) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 102
         at Admin.NET.Core.EnumToDictJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 43
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
         --- End of inner exception stack trace ---
fail: 2025-07-29 09:33:43.1244398 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:47.2014465 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:53.3623832 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      The JobDetail of <job_log> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:57.4553834 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:03.6001602 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <trigger_log> trigger of <job_log> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:05.6197690 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:09.7209992 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:11.7591660 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:15.8383858 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:17.8919209 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:19.9653624 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List<DeviceInfo> thirdPartyDevices)
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:24.0003194 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:26.0398365 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List<APOutput> thirdPartyAPs)
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:28.0509446 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <SyncDingTalkRoleJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:30.0783822 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] List<Devices> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:34.1410743 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:36.1877715 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<AccessPoints> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:38.2249984 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <SyncDingTalkUserJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:42.2937356 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:44.3269899 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <SyncDingTalkUserTrigger> trigger of <SyncDingTalkUserJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:46.3778778 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:50.4345340 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:52.4771102 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:54.5339041 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:58.5721158 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:00.6079354 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:02.6386767 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #25
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:04.6909688 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:08.7712982 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:10.7930480 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:12.8274283 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #25
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:16.8955255 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:18.9215019 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:22.9880140 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:24.9865045 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:29.0545258 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:31.0908193 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:33.1489814 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:37.2043906 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:39.2457417 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:41.2818131 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:43.2956272 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:47.3510809 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:49.3764562 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:51.3980977 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:55.4784232 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:57.5160379 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:01.5640945 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:03.5851703 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:05.6350053 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:09.7159210 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:11.7351706 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:13.7637553 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:17.7909168 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:19.8347428 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:23.8809257 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:27.9669828 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:29.9809521 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:34.0700196 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:36.0809027 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:38.1408078 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:42.2056216 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:44.2370643 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:46.2751520 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:50.4466631 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:52.4160974 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:54.4856629 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:58.5428384 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:00.5730508 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:02.6108204 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:06.7094053 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:08.7494115 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:10.7903004 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:14.8686635 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:16.9318464 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:21.0150285 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:23.0555351 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:27.1218918 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:29.1323894 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:31.1780867 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:35.2633312 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:37.2779081 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:39.3141531 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:43.3873217 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:45.3960470 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:47.4249838 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:51.4865520 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:53.4936195 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:55.5384894 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #27
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:59.5931400 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:01.6351078 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #27
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:05.7084819 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:07.7531685 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:11.7973463 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:13.8398077 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:15.8473196 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:19.9275783 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:21.9402239 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:23.9798231 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:28.0364492 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:30.0699630 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:32.0851230 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:36.1548056 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:38.1781194 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:42.2507365 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:44.2913010 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:48.3603482 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:50.3961368 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:52.4219856 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #21
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:56.4965567 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:58.5179647 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #21
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:00.5546925 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:04.6454474 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:06.6568591 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:08.7064770 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:12.7703341 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:14.8013991 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:36.0460869 +08:00 Tuesday L System.Logging.StringLogging[0] #27
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-29 9:39:35——错误SQL】
      
      [Sql]:SELECT `ap_id`,`device_id`,`device_name`,`mac_address`,`device_type`,`status`,`battery_level`,`signal_strength`,`firmware_version`,`BindTenantId`,`BindUserId`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `devices`  WHERE NOT ( `IsDelete`=1 )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:50.8122419 +08:00 Tuesday L System.Logging.StringLogging[0] #27
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-29 9:39:50——错误SQL】
      
      [Sql]:SELECT `ap_id`,`device_id`,`device_name`,`mac_address`,`device_type`,`status`,`battery_level`,`signal_strength`,`firmware_version`,`BindTenantId`,`BindUserId`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `devices`  WHERE NOT ( `IsDelete`=1 )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:50.8180224 +08:00 Tuesday L Admin.NET.Application.Service.APSyncBackgroundService[0] #27
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取本地设备数据 - 执行时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.<SyncDeviceStatusAsync>b__17_2() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 405
         at Admin.NET.Application.Service.APSyncBackgroundService.ExecuteWithRetryAsync[T](Func`1 operation, String operationName, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 676
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:50.8209203 +08:00 Tuesday L Admin.NET.Application.Service.APSyncBackgroundService[0] #27
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      获取本地设备数据失败，已达到最大重试次数
fail: 2025-07-29 09:44:25.0121889 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #27
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List<DeviceInfo> thirdPartyDevices)
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.IO.IOException: Unable to read data from the transport connection: 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
         --- End of inner exception stack trace ---
         at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
         at NewLife.Caching.RedisClient.GetResponse(Stream ns, Int32 count)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c__DisplayClass111_0`1.<Set>b__1(RedisClient rds, String k)
         at NewLife.Caching.Redis.Execute[TResult](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:46:39.9625583 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #32
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List<APOutput> thirdPartyAPs)
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.IO.IOException: Unable to read data from the transport connection: 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
         --- End of inner exception stack trace ---
         at System.Net.Sockets.NetworkStream.Read(Byte[] buffer, Int32 offset, Int32 count)
         at NewLife.Caching.RedisClient.GetResponse(Stream ns, Int32 count)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c__DisplayClass111_0`1.<Set>b__1(RedisClient rds, String k)
         at NewLife.Caching.Redis.Execute[TResult](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
