fail: 2025-07-29 09:33:35.3090289 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:37.0588858 +08:00 Tuesday L System.Logging.ScheduleService[0] #13
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Error occurred executing in <job_onlineUser> 清理在线用户 [S] <job_onlineUser trigger_onlineUser> 1s 清理在线用户 1ts 2025-07-29 09:33:28.807 +08:00 [Overrun].      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.OnlineUserJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\OnlineUserJob.cs:line 32
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:37.0948961 +08:00 Tuesday L Admin.NET.Core.Service.JobMonitor[0] #13
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      定时任务【清理在线用户】错误：System.InvalidOperationException: Error occurred executing in <job_onlineUser> 清理在线用户 [S] <job_onlineUser trigger_onlineUser> 1s 清理在线用户 1ts 2025-07-29 09:33:28.807 +08:00 [Overrun].
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.OnlineUserJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\OnlineUserJob.cs:line 32
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
         --- End of inner exception stack trace ---
fail: 2025-07-29 09:33:39.0781500 +08:00 Tuesday L System.Logging.ScheduleService[0] #23
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Error occurred executing in <job_EnumToDictJob> 枚举转字典 [S] <job_EnumToDictJob trigger_EnumToDictJob> 1s 枚举转字典 1ts 2025-07-29 09:33:28.697 +08:00 [Overrun].      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.EnumToDictJob.SyncEnumToDictInfoAsync(SqlSugarClient db, List`1 list) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 102
         at Admin.NET.Core.EnumToDictJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 43
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:39.1142064 +08:00 Tuesday L Admin.NET.Core.Service.JobMonitor[0] #23
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      定时任务【枚举转字典】错误：System.InvalidOperationException: Error occurred executing in <job_EnumToDictJob> 枚举转字典 [S] <job_EnumToDictJob trigger_EnumToDictJob> 1s 枚举转字典 1ts 2025-07-29 09:33:28.697 +08:00 [Overrun].
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.DeleteableProvider`1.<RemoveDataCache>b__67_0()
         at SqlSugar.DeleteableProvider`1.After(String sql)
         at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.EnumToDictJob.SyncEnumToDictInfoAsync(SqlSugarClient db, List`1 list) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 102
         at Admin.NET.Core.EnumToDictJob.ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Job\EnumToDictJob.cs:line 43
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_3.<<BackgroundProcessing>b__3>d.MoveNext()
      --- End of stack trace from previous location ---
         at Furion.FriendlyException.Retry.InvokeAsync(Func`1 action, Int32 numRetries, Int32 retryTimeout, Boolean finalThrow, Type[] exceptionTypes, Func`2 fallbackPolicy, Action`2 retryAction)
         at Furion.Schedule.ScheduleHostedService.<>c__DisplayClass23_2.<<BackgroundProcessing>b__2>d.MoveNext()
         --- End of inner exception stack trace ---
fail: 2025-07-29 09:33:43.1298194 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:47.2024049 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:53.3715760 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      The JobDetail of <job_log> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:33:57.4561632 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:03.6047475 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <trigger_log> trigger of <job_log> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:05.6204029 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:09.7250399 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:11.7596560 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:15.8402420 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:17.9003966 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:19.9756711 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List<DeviceInfo> thirdPartyDevices)
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:24.0012455 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:26.0505356 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] void Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List<APOutput> thirdPartyAPs)
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:28.0572976 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <SyncDingTalkRoleJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:30.0889905 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] List<Devices> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:34.1423017 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:36.1971347 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] List<AccessPoints> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:38.2284676 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      The JobDetail of <SyncDingTalkUserJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:42.2944478 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] List<DeviceInfo> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:44.3318953 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogError(Exception ex, string message, params object[] args)
      Persistence of <SyncDingTalkUserTrigger> trigger of <SyncDingTalkUserJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:46.3786074 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] List<APOutput> Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:50.4387036 +08:00 Tuesday L System.Logging.ScheduleService[0] #25
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:52.4787355 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:54.5439454 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:34:58.5762923 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:00.6193379 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:02.6395342 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #25
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:04.7008182 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:08.7750123 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:10.8057105 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:12.8278548 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #25
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:16.8990098 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:18.9219049 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:22.9917082 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_onlineUser> trigger of <job_onlineUser> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:24.9869971 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:29.0578333 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      Persistence of <trigger_EnumToDictJob> trigger of <job_EnumToDictJob> job failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnTriggerChangedAsync(PersistenceTriggerContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 164
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:31.0915443 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:33.1581358 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.<>c.<GetInfo>b__110_1(RedisClient rds)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.Redis.get_Info()
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:37.2087505 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_onlineUser> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:39.2471963 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:41.2932445 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:43.3056882 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #13
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:47.3556927 +08:00 Tuesday L System.Logging.ScheduleService[0] #32
      [Furion.Pure.dll] async Task Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      The JobDetail of <job_EnumToDictJob> persist failed.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Search(SearchModel model)
         at NewLife.Caching.FullRedis.Search(String pattern, Int32 count)
         at Admin.NET.Core.Service.SysCacheService.GetKeysByPrefixKey(String prefixKey) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 233
         at Admin.NET.Core.SqlSugarCache.GetAllKey[V]() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Cache\SqlSugarCache.cs:line 41
         at SqlSugar.CacheSchemeMain.RemoveCache(ICacheService cacheService, String tableName)
         at SqlSugar.UpdateableProvider`1.<RemoveDataCache>b__145_0()
         at SqlSugar.UpdateableProvider`1.After(String sql)
         at SqlSugar.UpdateableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.Service.DbJobPersistence.OnChangedAsync(PersistenceContext context) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Job\DbJobPersistence.cs:line 137
         at Furion.Schedule.SchedulerFactory.ProcessQueueAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:49.3767940 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:51.4063922 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:55.4792865 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:35:57.5164481 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:01.5739107 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:03.5856373 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:05.6442735 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:09.7256657 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #24
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:11.7451451 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:13.7780632 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:17.7925437 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:19.8429890 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:23.8815984 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:27.9805279 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:29.9821287 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:34.0796852 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:36.0817130 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:38.1512719 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:42.2174179 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:44.2456613 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:46.2760943 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:50.4927146 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:52.4168602 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:54.5051095 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:36:58.5548279 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:00.5741082 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:02.6216241 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:06.7189776 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #31
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:08.7499146 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:10.8067818 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:14.8694346 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:16.9468438 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:21.0155769 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:23.0672221 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:27.1317279 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:29.1423873 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:31.1791895 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:35.2712095 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:37.2786700 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:39.3251135 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:43.3952969 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:45.3964871 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:47.4336812 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:51.4930034 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #35
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:53.4943802 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:55.5460549 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #27
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:37:59.5939583 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:01.6458635 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #27
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:05.7092028 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:07.7606164 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:11.8058941 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:13.8487589 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:15.8479512 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:19.9377728 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Get[T](String key)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:21.9406403 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:23.9902494 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:28.0439710 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:30.0777290 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #11
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:32.0859532 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:36.1631868 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:38.1786559 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #30
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:42.2610691 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:44.2923823 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:48.3700397 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:50.3965971 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:52.4309577 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #21
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:56.5032869 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:38:58.5244500 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #21
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:00.5553132 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取第三方AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyAPs() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 259
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:04.6542082 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #9
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 153
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:06.6577894 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      从缓存获取第三方设备数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetThirdPartyDevices() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 293
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:08.7227232 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      设置第三方AP数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyAPs(List`1 thirdPartyAPs) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 276
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:12.7787233 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #3
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncDeviceStatusAsync()
      设置第三方设备数据到缓存失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at NewLife.Caching.Redis.Set[T](String key, T value, Int32 expire)
         at NewLife.Caching.Cache.Set[T](String key, T value, TimeSpan expire)
         at Admin.NET.Core.Service.SysCacheService.Set(String key, Object value, TimeSpan expire) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 81
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.SetThirdPartyDevices(List`1 thirdPartyDevices) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 310
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-29 09:39:14.8089595 +08:00 Tuesday L Admin.NET.Application.Service.Cache.APDeviceCacheService[0] #26
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      从缓存获取AP数据失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。 [::ffff:127.0.0.1]:6379
         at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
      --- End of stack trace from previous location ---
         at System.Net.Sockets.Socket.Connect(IPAddress[] addresses, Int32 port)
         at System.Net.Sockets.TcpClient.Connect(IPAddress[] ipAddresses, Int32 port)
         at NewLife.Caching.RedisClient.GetStream(Boolean create)
         at NewLife.Caching.RedisClient.ExecuteCommand(String cmd, Object[] args)
         at NewLife.Caching.RedisClient.Execute[TResult](String cmd, Object[] args)
         at NewLife.Caching.Redis.Execute[TResult](Func`2 func)
         at NewLife.Caching.Redis.GetInfo(Boolean all)
         at NewLife.Caching.FullRedis.InitCluster()
         at NewLife.Caching.FullRedis.Execute[T](String key, Func`3 func, Boolean write)
         at Admin.NET.Core.Service.SysCacheService.Get[T](String key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Cache\SysCacheService.cs:line 171
         at Admin.NET.Application.Service.Cache.APDeviceCacheService.GetAllAccessPoints() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Cache\APDeviceCacheService.cs:line 46
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
