# AP和设备同步服务缓存优化说明

## 概述

为了减少MySQL数据库压力，提高系统性能，我们为AP和设备同步服务添加了Redis缓存支持。通过缓存机制，大幅减少了数据库查询次数，提升了系统响应速度。

## 主要改进

### 1. 启用Redis缓存
- 修改 `Admin.NET.Application/Configuration/Cache.json`
- 将 `CacheType` 从 `Memory` 改为 `Redis`
- 优化数据库连接池配置，减少连接数和超时时间

### 2. 缓存常量定义
在 `Admin.NET.Core/Const/CacheConst.cs` 中添加了以下缓存键常量：
- `KeyAccessPoints`: AP接入点缓存
- `KeyDevices`: 设备缓存  
- `KeyAPStatus`: AP状态缓存
- `KeyDeviceStatus`: 设备状态缓存
- `KeyThirdPartyAPs`: 第三方平台AP数据缓存
- `KeyThirdPartyDevices`: 第三方平台设备数据缓存

### 3. 专用缓存服务
创建了 `APDeviceCacheService` 类，提供以下功能：

#### AP缓存操作
- `GetAllAccessPoints()`: 获取所有AP数据（优先从缓存）
- `SetAllAccessPoints()`: 设置AP数据到缓存，并建立MAC地址索引
- `GetAccessPointByMac()`: 根据MAC地址获取AP数据
- `UpdateAccessPointsStatus()`: 更新AP状态缓存

#### 设备缓存操作
- `GetAllDevices()`: 获取所有设备数据（优先从缓存）
- `SetAllDevices()`: 设置设备数据到缓存，并建立MAC地址索引
- `GetDeviceByMac()`: 根据MAC地址获取设备数据
- `UpdateDevicesStatus()`: 更新设备状态缓存

#### 第三方数据缓存
- `GetThirdPartyAPs()` / `SetThirdPartyAPs()`: 第三方AP数据缓存
- `GetThirdPartyDevices()` / `SetThirdPartyDevices()`: 第三方设备数据缓存

#### 缓存清理
- `ClearAllAPCache()`: 清除所有AP相关缓存
- `ClearAllDeviceCache()`: 清除所有设备相关缓存

### 4. 缓存过期时间策略
- **默认缓存**: 5分钟（本地数据）
- **第三方数据缓存**: 2分钟（第三方平台数据）
- **状态缓存**: 1分钟（实时状态数据）

### 5. 后台同步服务优化

#### APSyncBackgroundService 改进
- **第三方数据获取**: 优先从缓存获取，缓存未命中时才调用第三方API
- **本地数据获取**: 优先从缓存获取，缓存未命中时才查询数据库
- **状态更新**: 数据库更新成功后，同步更新缓存
- **同步间隔**: 从10秒调整为15秒，减少数据库访问频率
- **连接池优化**: 并发连接数从5个降低到2个，使用更保守的策略

#### 数据库连接优化
- 优化MySQL连接字符串参数：
  - `MinimumPoolSize`: 5 → 2
  - `MaximumPoolSize`: 50 → 20  
  - `ConnectionIdleTimeout`: 180 → 60
  - `ConnectionLifeTime`: 300 → 180
  - 添加 `ConnectionReset=true`

### 6. 业务服务缓存集成

#### AccessPointsService 改进
- 在增加、更新、删除AP后自动清除相关缓存
- 确保数据一致性，避免脏数据

#### DevicesService 改进  
- 在增加、更新、删除设备后自动清除相关缓存
- 同步操作完成后根据变更情况清除缓存

## 性能提升效果

### 数据库压力减少
1. **查询次数减少**: 缓存命中时避免数据库查询
2. **连接数优化**: 并发连接数从5个降低到2个
3. **同步频率优化**: 同步间隔从10秒增加到15秒

### 响应速度提升
1. **缓存命中**: Redis内存访问速度远快于MySQL查询
2. **索引优化**: 按MAC地址建立缓存索引，快速定位数据
3. **批量操作**: 减少单次操作的数据库交互

### 系统稳定性增强
1. **连接冲突减少**: 通过信号量和重试机制处理连接冲突
2. **错误恢复**: 缓存失效时自动回退到数据库查询
3. **数据一致性**: 数据变更时及时清除相关缓存

## 使用建议

### 监控指标
1. **缓存命中率**: 监控Redis缓存的命中情况
2. **数据库连接数**: 观察连接池使用情况
3. **同步延迟**: 监控数据同步的时间间隔

### 运维注意事项
1. **Redis可用性**: 确保Redis服务稳定运行
2. **内存使用**: 监控Redis内存使用情况
3. **缓存清理**: 必要时可手动清理缓存

### 扩展建议
1. **分布式缓存**: 支持多实例部署的缓存同步
2. **缓存预热**: 系统启动时预加载热点数据
3. **智能过期**: 根据数据访问频率动态调整过期时间

## 配置说明

### Redis配置
```json
{
  "Cache": {
    "CacheType": "Redis",
    "Redis": {
      "Configuration": "server=127.0.0.1:6379;password=;db=5;"
    }
  }
}
```

### 数据库连接配置
```json
{
  "ConnectionString": "Server=host;PORT=port;Database=db;Uid=user;Pwd=pass;Pooling=true;MinimumPoolSize=2;MaximumPoolSize=20;ConnectionTimeout=30;ConnectionIdleTimeout=60;ConnectionLifeTime=180;ConnectionReset=true;"
}
```

## 总结

通过引入Redis缓存和优化数据库连接配置，我们成功减少了MySQL数据库的压力，提升了系统的整体性能和稳定性。缓存机制在保证数据一致性的同时，显著提高了数据访问速度，为系统的高并发场景提供了有力支撑。
