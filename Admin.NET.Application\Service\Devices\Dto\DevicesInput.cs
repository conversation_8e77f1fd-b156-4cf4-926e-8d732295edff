// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备基础输入参数
/// </summary>
public class DevicesBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 网关ID
    /// </summary>
    public virtual long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public virtual string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    public virtual string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>
    public virtual string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public virtual int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public virtual int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>
    public virtual int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public virtual string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public virtual long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public virtual long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备分页查询输入参数
/// </summary>
public class PageDevicesInput : BasePageInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    public string? device_name { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备增加输入参数
/// </summary>
public class AddDevicesInput
{
    /// <summary>
    /// 网关ID
    /// </summary>
    public long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(100, ErrorMessage = "设备ID字符长度不能超过100")]
    public string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "设备名称字符长度不能超过200")]
    public string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>
    [MaxLength(17, ErrorMessage = "MAC地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>
    public int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备删除输入参数
/// </summary>
public class DeleteDevicesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备更新输入参数
/// </summary>
public class UpdateDevicesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 网关ID
    /// </summary>    
    public long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>    
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(100, ErrorMessage = "设备ID字符长度不能超过100")]
    public string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "设备名称字符长度不能超过200")]
    public string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>    
    [MaxLength(17, ErrorMessage = "MAC地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>    
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>    
    public int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>    
    public int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>    
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>    
    public long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>    
    public long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备主键查询输入参数
/// </summary>
public class QueryByIdDevicesInput : DeleteDevicesInput
{
}

/// <summary>
/// 从第三方接口同步设备输入参数
/// </summary>
public class SyncDevicesInput
{
    /// <summary>
    /// 页码（默认1）
    /// </summary>
    public int? PageNo { get; set; } = 1;

    /// <summary>
    /// 页大小（默认100）
    /// </summary>
    public int? PageSize { get; set; } = 100;

    /// <summary>
    /// 标签MAC地址（用于精确查询）
    /// </summary>
    public string? LabelMac { get; set; }

    /// <summary>
        /// 会议室编号
        /// </summary>
        public int? RoomId { get; set; }

    /// <summary>
    /// 人员编号
    /// </summary>
    public string? StaffCode { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 默认网关ID（为同步的设备分配网关）
    /// </summary>
    public long? DefaultApId { get; set; }

    /// <summary>
    /// 是否更新已存在的设备（默认false）
    /// </summary>
    public bool UpdateExisting { get; set; } = false;
}

/// <summary>
/// 同步设备结果
/// </summary>
public class SyncDevicesResult
{
    /// <summary>
    /// 查询到的总数量
    /// </summary>
    public int TotalQueried { get; set; }

    /// <summary>
    /// 新增数量
    /// </summary>
    public int AddedCount { get; set; }

    /// <summary>
    /// 更新数量
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// 跳过数量
    /// </summary>
    public int SkippedCount { get; set; }

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// 同步详情
    /// </summary>
    public List<SyncDeviceDetail> Details { get; set; } = new List<SyncDeviceDetail>();
}

/// <summary>
/// 同步设备详情
/// </summary>
public class SyncDeviceDetail
{
    /// <summary>
    /// MAC地址
    /// </summary>
    public string? MacAddress { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 第三方设备状态
    /// </summary>
    public int? ThirdPartyStatus { get; set; }

    /// <summary>
    /// 本地设备ID
    /// </summary>
    public long? LocalDeviceId { get; set; }

    /// <summary>
    /// 处理结果：新增、更新、跳过、失败、错误
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }
}

