﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.Cache;
using Admin.NET.Core.Service;
using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Service.Dto;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class DevicesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Devices> _devicesRep;
    private readonly SqlSugarRepository<DeviceBindings> _deviceBindingsRep;
    private readonly SqlSugarRepository<MeetingRooms> _meetingRoomsRep;
    private readonly SqlSugarRepository<AccessPoints> _accessPointsRep;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<DevicesService> _logger;
    private readonly APDeviceCacheService _cacheService;

    public DevicesService(SqlSugarRepository<Devices> devicesRep,
                         SqlSugarRepository<DeviceBindings> deviceBindingsRep,
                         SqlSugarRepository<MeetingRooms> meetingRoomsRep,
                         SqlSugarRepository<AccessPoints> accessPointsRep,
                         GreenDisplayService greenDisplayService,
                         ILogger<DevicesService> logger,
                         APDeviceCacheService cacheService)
    {
        _devicesRep = devicesRep;
        _deviceBindingsRep = deviceBindingsRep;
        _meetingRoomsRep = meetingRoomsRep;
        _accessPointsRep = accessPointsRep;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 分页查询蓝牙桌牌设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DevicesOutput>> Page(PageDevicesInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _devicesRep.AsQueryable()
            .LeftJoin<DeviceBindings>((d, db) => d.Id == db.device_id && !db.IsDelete)
            .LeftJoin<AccessPoints>((d, db, ap) => d.ap_id == ap.Id && !ap.IsDelete)
            .Where((d, db, ap) => !d.IsDelete)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), (d, db, ap) => d.device_name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.device_name), (d, db, ap) => d.device_name.Contains(input.device_name.Trim()))
            .WhereIF(input.device_type != null, (d, db, ap) => d.device_type == input.device_type)
            .WhereIF(input.status != null, (d, db, ap) => d.status == input.status)
            .Select((d, db, ap) => new DevicesOutput
            {
                Id = d.Id,
                ap_id = d.ap_id,
                apMac = ap.mac_address,
                device_id = d.device_id,
                device_name = d.device_name,
                mac_address = d.mac_address,
                device_type = d.device_type,
                status = d.status,
                battery_level = d.battery_level,
                signal_strength = d.signal_strength,
                firmware_version = d.firmware_version,
                BindTenantId = d.BindTenantId,
                BindUserId = d.BindUserId,
                IsDelete = d.IsDelete,
                CreateTime = d.CreateTime,
                UpdateTime = d.UpdateTime,
                CreateUserId = d.CreateUserId,
                CreateUserName = d.CreateUserName,
                UpdateUserId = d.UpdateUserId,
                UpdateUserName = d.UpdateUserName,
                BindingInfo = db.Id == null ? null : new DeviceBindingInfo
                {
                    BindingId = db.Id,
                    StaffId = db.staff_id,
                    TemplateId = db.template_id,
                    MeetingRoomId = db.meeting_room_id,
                    BindingType = db.binding_type,
                    BindingTypeName = db.binding_type == 1 ? "员工" : db.binding_type == 2 ? "房间" : db.binding_type == 3 ? "模版" : "未知",
                    BindingData = db.binding_data,
                    BindingCreateTime = db.CreateTime
                }
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取蓝牙桌牌设备详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取蓝牙桌牌设备详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Devices> Detail([FromQuery] QueryByIdDevicesInput input)
    {
        return await _devicesRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加蓝牙桌牌设备 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDevicesInput input)
    {
        var entity = input.Adapt<Devices>();

        var result = await _devicesRep.InsertAsync(entity) ? entity.Id : 0;

        if (result > 0)
        {
            // 清除设备缓存，确保下次查询时获取最新数据
            _cacheService.ClearAllDeviceCache();
        }

        return result;
    }

    /// <summary>
    /// 从第三方接口全量同步设备数据 🔄
    /// </summary>
    /// <returns>同步结果</returns>
    [DisplayName("从第三方接口全量同步设备数据")]
    [ApiDescriptionSettings(Name = "SyncFromThirdParty"), HttpPost]
    public async Task<SyncDevicesResult> SyncDevicesFromThirdParty()
    {
        var result = new SyncDevicesResult
        {
            TotalQueried = 0,
            AddedCount = 0,
            UpdatedCount = 0,
            SkippedCount = 0,
            ErrorCount = 0,
            Details = new List<SyncDeviceDetail>()
        };

        try
        {
            _logger.LogInformation("开始从第三方接口全量同步设备数据");

            var pageSize = 100; // 每页数量，默认100
            var currentPage = 1;
            var allThirdPartyDevices = new List<DeviceInfo>();
            var hasMoreData = true;

            // 分页获取所有第三方设备数据
            while (hasMoreData)
            {
                var queryInput = new QueryDevicesInput
                {
                    PageNo = currentPage,
                    PageSize = pageSize
                };

                _logger.LogInformation("正在获取第 {Page} 页数据，每页 {PageSize} 条", currentPage, pageSize);
                var thirdPartyResponse = await _greenDisplayService.QueryDevicesAsync(queryInput);
                
                if (thirdPartyResponse?.list == null || !thirdPartyResponse.list.Any())
                {
                    _logger.LogInformation("第 {Page} 页无数据，停止分页查询", currentPage);
                    hasMoreData = false;
                    break;
                }

                allThirdPartyDevices.AddRange(thirdPartyResponse.list);
                _logger.LogInformation("第 {Page} 页获取到 {Count} 个设备", currentPage, thirdPartyResponse.list.Count);

                // 如果返回的数据少于页大小，说明已经是最后一页
                if (thirdPartyResponse.list.Count < pageSize)
                {
                    hasMoreData = false;
                }
                else
                {
                    currentPage++;
                }
            }

            if (!allThirdPartyDevices.Any())
            {
                _logger.LogWarning("第三方接口未返回任何设备数据");
                return result;
            }

            result.TotalQueried = allThirdPartyDevices.Count;
            _logger.LogInformation("从第三方接口总共查询到 {Count} 个设备", result.TotalQueried);

            // 获取本地已存在的设备（通过MAC地址匹配）
            var macAddresses = allThirdPartyDevices
                .Where(d => !string.IsNullOrEmpty(d.labelMac))
                .Select(d => d.labelMac)
                .ToList();

            var existingDevices = await _devicesRep.AsQueryable()
                .Where(d => macAddresses.Contains(d.mac_address) && !d.IsDelete)
                .ToListAsync();

            var existingDeviceDict = existingDevices.ToDictionary(d => d.mac_address, d => d);

            _logger.LogInformation("开始处理 {Count} 个第三方设备，本地已存在 {ExistingCount} 个设备", 
                allThirdPartyDevices.Count, existingDevices.Count);

            // 分类处理设备：新增、更新、跳过
            var devicesForInsert = new List<Devices>();
            var devicesForUpdate = new List<Devices>();
            
            foreach (var thirdPartyDevice in allThirdPartyDevices)
            {
                var detail = new SyncDeviceDetail
                {
                    MacAddress = thirdPartyDevice.labelMac,
                    DeviceName = !string.IsNullOrEmpty(thirdPartyDevice.labelMac) ? 
                        $"设备_{thirdPartyDevice.labelMac.Substring(thirdPartyDevice.labelMac.Length - 6)}" : 
                        "未知设备",
                    ThirdPartyStatus = thirdPartyDevice.result
                };

                try
                {
                    // 检查MAC地址是否有效
                    if (string.IsNullOrEmpty(thirdPartyDevice.labelMac))
                    {
                        detail.Result = "跳过"; 
                        detail.Message = "MAC地址为空";
                        result.SkippedCount++;
                        result.Details.Add(detail);
                        continue;
                    }

                    var accessPoint = await _accessPointsRep.GetFirstAsync(x => x.mac_address == thirdPartyDevice.apMac);
                    var APID = accessPoint?.Id; 

                    // 转换第三方设备数据为本地设备实体
                    var deviceEntity = ConvertThirdPartyDeviceToLocal(thirdPartyDevice, APID);

                    if (existingDeviceDict.TryGetValue(thirdPartyDevice.labelMac, out var existingDevice))
                    {
                        // 更新现有设备（无条件更新）
                        // 保留原有的ID和创建信息
                        deviceEntity.Id = existingDevice.Id;
                        deviceEntity.CreateTime = existingDevice.CreateTime;
                        deviceEntity.CreateUserId = existingDevice.CreateUserId;
                        deviceEntity.CreateUserName = existingDevice.CreateUserName;
                        
                        // 保留原有的网关ID
                        if (existingDevice.ap_id.HasValue)
                        {
                            deviceEntity.ap_id = existingDevice.ap_id;
                        }

                        devicesForUpdate.Add(deviceEntity);
                        detail.Result = "更新";
                        detail.Message = "设备信息已更新";
                        detail.LocalDeviceId = existingDevice.Id;
                        result.UpdatedCount++;
                    }
                    else
                    {
                        // 准备新增设备
                        devicesForInsert.Add(deviceEntity);
                        detail.Result = "新增";
                        detail.Message = "设备已准备添加";
                        result.AddedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理设备时发生异常，MAC: {Mac}", thirdPartyDevice.labelMac);
                    detail.Result = "错误";
                    detail.Message = $"处理异常: {ex.Message}";
                    result.ErrorCount++;
                }

                result.Details.Add(detail);
            }

            // 批量插入新设备
            if (devicesForInsert.Any())
            {
                try
                {
                    _logger.LogInformation("开始批量插入 {Count} 个新设备", devicesForInsert.Count);
                    var insertResult = await _devicesRep.InsertRangeAsync(devicesForInsert);
                    if (!insertResult)
                    {
                        _logger.LogError("批量插入设备失败");
                        // 更新失败的设备详情
                        foreach (var detail in result.Details.Where(d => d.Result == "新增"))
                        {
                            detail.Result = "失败";
                            detail.Message = "批量插入失败";
                        }
                        result.ErrorCount += result.AddedCount;
                        result.AddedCount = 0;
                    }
                    else
                    {
                        _logger.LogInformation("成功批量插入 {Count} 个新设备", devicesForInsert.Count);
                        // 更新设备ID到详情中
                        var insertedDeviceDict = devicesForInsert.ToDictionary(d => d.mac_address, d => d.Id);
                        foreach (var detail in result.Details.Where(d => d.Result == "新增"))
                        {
                            if (insertedDeviceDict.TryGetValue(detail.MacAddress, out var deviceId))
                            {
                                detail.LocalDeviceId = deviceId;
                                detail.Message = "设备已成功添加";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量插入设备时发生异常");
                    // 更新失败的设备详情
                    foreach (var detail in result.Details.Where(d => d.Result == "新增"))
                    {
                        detail.Result = "错误";
                        detail.Message = $"批量插入异常: {ex.Message}";
                    }
                    result.ErrorCount += result.AddedCount;
                    result.AddedCount = 0;
                }
            }

            // 批量更新现有设备
            if (devicesForUpdate.Any())
            {
                try
                {
                    _logger.LogInformation("开始批量更新 {Count} 个现有设备", devicesForUpdate.Count);
                    var updateResult = await _devicesRep.UpdateRangeAsync(devicesForUpdate);
                    if (!updateResult)
                    {
                        _logger.LogError("批量更新设备失败");
                        // 更新失败的设备详情
                        foreach (var detail in result.Details.Where(d => d.Result == "更新"))
                        {
                            detail.Result = "失败";
                            detail.Message = "批量更新失败";
                        }
                        result.ErrorCount += result.UpdatedCount;
                        result.UpdatedCount = 0;
                    }
                    else
                    {
                        _logger.LogInformation("成功批量更新 {Count} 个现有设备", devicesForUpdate.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量更新设备时发生异常");
                    // 更新失败的设备详情
                    foreach (var detail in result.Details.Where(d => d.Result == "更新"))
                    {
                        detail.Result = "错误";
                        detail.Message = $"批量更新异常: {ex.Message}";
                    }
                    result.ErrorCount += result.UpdatedCount;
                    result.UpdatedCount = 0;
                }
            }

            _logger.LogInformation("设备同步完成，总查询: {Total}, 新增: {Added}, 更新: {Updated}, 跳过: {Skipped}, 错误: {Error}",
                result.TotalQueried, result.AddedCount, result.UpdatedCount, result.SkippedCount, result.ErrorCount);

            // 如果有数据变更，清除设备缓存
            if (result.AddedCount > 0 || result.UpdatedCount > 0)
            {
                _cacheService.ClearAllDeviceCache();
                _logger.LogDebug("已清除设备缓存，因为有 {Added} 个新增和 {Updated} 个更新", result.AddedCount, result.UpdatedCount);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从第三方接口同步设备时发生异常");
            result.ErrorCount = result.TotalQueried;
            throw Oops.Oh($"同步设备失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 将第三方设备数据转换为本地设备实体
    /// </summary>
    /// <param name="thirdPartyDevice">第三方设备信息</param>
    /// <param name="defaultApId">默认网关ID</param>
    /// <returns>本地设备实体</returns>
    private Devices ConvertThirdPartyDeviceToLocal(DeviceInfo thirdPartyDevice, long? defaultApId)
    {
        // 解析设备状态：根据result字段判断设备状态
        int? deviceStatus = null;
        if (thirdPartyDevice.result >= 0)
        {
            // 根据第三方result字段映射到本地状态
            deviceStatus = thirdPartyDevice.result switch
            {
                0 => 0,  // 正常/在线
                1 => 1,  // 离线
                _ => null
            };
        }

        // 使用typeId作为设备类型
        int? deviceType = thirdPartyDevice.typeId > 0 ? thirdPartyDevice.typeId : null;

        // 生成设备名称（如果没有明确的名称字段，使用MAC地址）
        string deviceName = !string.IsNullOrEmpty(thirdPartyDevice.labelMac) ? 
            $"设备_{thirdPartyDevice.labelMac}" : 
            "未知设备";

        return new Devices
        {
            ap_id = defaultApId,
            device_id = thirdPartyDevice.labelMac, // 使用labelMac作为设备ID
            device_name = deviceName,
            mac_address = thirdPartyDevice.labelMac,
            device_type = deviceType,
            status = deviceStatus,
            battery_level = thirdPartyDevice.battery > 0 ? thirdPartyDevice.battery : null,
            signal_strength = !string.IsNullOrEmpty(thirdPartyDevice.signals) ? 
                (int.TryParse(thirdPartyDevice.signals, out int signal) ? signal : (int?)null) : null,
            firmware_version = null // DeviceInfo中没有版本信息字段
        };
    }

    /// <summary>
    /// 更新蓝牙桌牌设备 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDevicesInput input)
    {
        var entity = input.Adapt<Devices>();
        var result = await _devicesRep.AsUpdateable(entity)
        .ExecuteCommandAsync();

        if (result > 0)
        {
            // 清除设备缓存，确保下次查询时获取最新数据
            _cacheService.ClearAllDeviceCache();
        }
    }

    /// <summary>
    /// 删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDevicesInput input)
    {
        var entity = await _devicesRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        var result = await _devicesRep.FakeDeleteAsync(entity);   //假删除
        //var result = await _devicesRep.DeleteAsync(entity);   //真删除

        if (result > 0)
        {
            // 清除设备缓存，确保下次查询时获取最新数据
            _cacheService.ClearAllDeviceCache();
        }
    }

    /// <summary>
    /// 批量删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteDevicesInput> input)
    {
        var exp = Expressionable.Create<Devices>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _devicesRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        var result = await _devicesRep.FakeDeleteAsync(list);   //假删除
        //var result = await _devicesRep.DeleteAsync(list);   //真删除

        if (result > 0)
        {
            // 清除设备缓存，确保下次查询时获取最新数据
            _cacheService.ClearAllDeviceCache();
        }

        return result;
    }

    #region 统计数据
    /// <summary>
    /// 获取设备统计信息 📊
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取设备统计信息")]
    [ApiDescriptionSettings(Name = "Statistics"), HttpGet]
    public async Task<DeviceStatisticsOutput> GetDeviceStatistics()
    {
        try
        {
            // 查询所有未删除的设备
            var allDevices = await _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete)
                .ToListAsync();
            var totalDevices = allDevices.Count;
            if (totalDevices == 0)
            {
                return new DeviceStatisticsOutput
                {
                    TotalDevices = 0,
                    OnlineDevices = 0,
                    OfflineDevices = 0,
                    LowBatteryDevices = 0,
                    WeakSignalDevices = 0,
                    OnlineRate = 0,
                    LowBatteryRate = 0,
                    WeakSignalRate = 0
                };
            }
            // 统计在线设备（status = 0表示在线）
            var onlineDevices = allDevices.Count(d => d.status == 0);

            // 统计离线设备（status = 1表示离线）
            var offlineDevices = allDevices.Count(d => d.status == 1);

            // 统计低电量设备（电量低于20%）
            var lowBatteryDevices = allDevices.Count(d => d.battery_level.HasValue && d.battery_level < 20);

            // 统计弱信号设备（信号弱于-80dBm）
            var weakSignalDevices = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80);

            // 计算百分比
            var onlineRate = totalDevices > 0 ? Math.Round((decimal)onlineDevices / totalDevices * 100, 2) : 0;
            var lowBatteryRate = totalDevices > 0 ? Math.Round((decimal)lowBatteryDevices / totalDevices * 100, 2) : 0;
            var weakSignalRate = totalDevices > 0 ? Math.Round((decimal)weakSignalDevices / totalDevices * 100, 2) : 0;

            var result = new DeviceStatisticsOutput
            {
                TotalDevices = totalDevices,
                OnlineDevices = onlineDevices,
                OfflineDevices = offlineDevices,
                LowBatteryDevices = lowBatteryDevices,
                WeakSignalDevices = weakSignalDevices,
                OnlineRate = onlineRate,
                LowBatteryRate = lowBatteryRate,
                WeakSignalRate = weakSignalRate
            };

            _logger.LogInformation("设备统计查询成功，总设备数：{TotalDevices}，在线设备：{OnlineDevices}，低电量设备：{LowBatteryDevices}，弱信号设备：{WeakSignalDevices}",
                totalDevices, onlineDevices, lowBatteryDevices, weakSignalDevices);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备统计信息时发生错误");
            throw Oops.Oh("获取设备统计信息失败");
        }
    }

    /// <summary>
    /// 获取设备详细统计信息 📊
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取设备详细统计信息")]
    [ApiDescriptionSettings(Name = "DetailedStatistics"), HttpGet]
    public async Task<DeviceDetailedStatisticsOutput> GetDeviceDetailedStatistics()
    {
        try
        {
            // 查询所有未删除的设备
            var allDevices = await _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete)
                .ToListAsync();

            // 获取基础统计信息
            var basicStats = await GetDeviceStatistics();

            // 按设备类型分组统计
            var deviceTypeStats = allDevices
                .GroupBy(d => d.device_type ?? 0)
                .Select(g => new DeviceTypeStatistics
                {
                    DeviceType = g.Key,
                    DeviceTypeName = g.Key == 1 ? "姓名桌牌" : g.Key == 2 ? "价格标签" : "未知类型",
                    TotalCount = g.Count(),
                    OnlineCount = g.Count(d => d.status == 0),
                    OfflineCount = g.Count(d => d.status == 1),
                    LowBatteryCount = g.Count(d => d.battery_level.HasValue && d.battery_level < 20),
                    WeakSignalCount = g.Count(d => d.signal_strength.HasValue && d.signal_strength < -80)
                })
                .ToList();

            // 电量分布统计
            var batteryStats = new BatteryDistributionStatistics
            {
                Battery0To20 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level >= 0 && d.battery_level <= 20),
                Battery21To50 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 20 && d.battery_level <= 50),
                Battery51To80 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 50 && d.battery_level <= 80),
                Battery81To100 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 80 && d.battery_level <= 100),
                AverageBattery = allDevices.Where(d => d.battery_level.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.battery_level.HasValue).Average(d => d.battery_level.Value), 2)
                    : 0
            };

            // 信号强度分布统计
            var signalStats = new SignalDistributionStatistics
            {
                SignalBelow80 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80),
                Signal80To60 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -80 && d.signal_strength < -60),
                Signal60To40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -60 && d.signal_strength < -40),
                SignalAbove40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -40),
                AverageSignal = allDevices.Where(d => d.signal_strength.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.signal_strength.HasValue).Average(d => d.signal_strength.Value), 2)
                    : 0
            };

            var result = new DeviceDetailedStatisticsOutput
            {
                BasicStatistics = basicStats,
                DeviceTypeStatistics = deviceTypeStats,
                BatteryDistribution = batteryStats,
                SignalDistribution = signalStats
            };

            _logger.LogInformation("设备详细统计查询成功，总设备数：{TotalDevices}，设备类型数：{TypeCount}",
                basicStats.TotalDevices, deviceTypeStats.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备详细统计信息时发生错误");
            throw Oops.Oh("获取设备详细统计信息失败");
        }
    }

    /// <summary>
    /// 根据条件获取设备统计信息 📊
    /// </summary>
    /// <param name="input">统计查询条件</param>
    /// <returns></returns>
    [DisplayName("根据条件获取设备统计信息")]
    [ApiDescriptionSettings(Name = "StatisticsByCondition"), HttpPost]
    public async Task<DeviceDetailedStatisticsOutput> GetDeviceStatisticsByCondition(DeviceStatisticsInput input)
    {
        try
        {
            // 构建设备查询条件
            var deviceQuery = _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete);

            // 按时间范围过滤
            if (input.StartTime.HasValue)
            {
                deviceQuery = deviceQuery.Where(d => d.CreateTime >= input.StartTime.Value);
            }

            if (input.EndTime.HasValue)
            {
                deviceQuery = deviceQuery.Where(d => d.CreateTime <= input.EndTime.Value);
            }

            // 按设备类型过滤
            if (input.DeviceType.HasValue)
            {
                deviceQuery = deviceQuery.Where(d => d.device_type == input.DeviceType.Value);
            }

            // 按网关ID过滤
            if (input.ApId.HasValue)
            {
                deviceQuery = deviceQuery.Where(d => d.ap_id == input.ApId.Value);
            }

            List<Devices> allDevices;

            // 按会议室ID过滤
            if (input.MeetingRoomId.HasValue)
            {
                if (input.MeetingRoomId.Value == 0)
                {
                    // 0表示所有会议室，查询所有设备
                    allDevices = await deviceQuery.ToListAsync();
                }
                else
                {
                    // 查询指定会议室绑定的设备
                    allDevices = await deviceQuery
                        .LeftJoin<DeviceBindings>((d, db) => d.Id == db.device_id && !db.IsDelete)
                        .Where((d, db) => db.meeting_room_id == input.MeetingRoomId.Value && db.binding_type == 2) // binding_type = 2 表示房间绑定
                        .Select((d, db) => d)
                        .ToListAsync();
                }
            }
            else
            {
                allDevices = await deviceQuery.ToListAsync();
            }
            var totalDevices = allDevices.Count;

            if (totalDevices == 0)
            {
                return new DeviceDetailedStatisticsOutput
                {
                    BasicStatistics = new DeviceStatisticsOutput(),
                    DeviceTypeStatistics = [],
                    BatteryDistribution = new BatteryDistributionStatistics(),
                    SignalDistribution = new SignalDistributionStatistics()
                };
            }

            // 计算基础统计
            var onlineDevices = allDevices.Count(d => d.status == 0);
            var offlineDevices = allDevices.Count(d => d.status == 1);
            var lowBatteryDevices = allDevices.Count(d => d.battery_level.HasValue && d.battery_level < 20);
            var weakSignalDevices = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80);

            var basicStats = new DeviceStatisticsOutput
            {
                TotalDevices = totalDevices,
                OnlineDevices = onlineDevices,
                OfflineDevices = offlineDevices,
                LowBatteryDevices = lowBatteryDevices,
                WeakSignalDevices = weakSignalDevices,
                OnlineRate = totalDevices > 0 ? Math.Round((decimal)onlineDevices / totalDevices * 100, 2) : 0,
                LowBatteryRate = totalDevices > 0 ? Math.Round((decimal)lowBatteryDevices / totalDevices * 100, 2) : 0,
                WeakSignalRate = totalDevices > 0 ? Math.Round((decimal)weakSignalDevices / totalDevices * 100, 2) : 0
            };

            // 按设备类型分组统计
            var deviceTypeStats = allDevices
                .GroupBy(d => d.device_type ?? 0)
                .Select(g => new DeviceTypeStatistics
                {
                    DeviceType = g.Key,
                    DeviceTypeName = g.Key == 1 ? "姓名桌牌" : g.Key == 2 ? "价格标签" : "未知类型",
                    TotalCount = g.Count(),
                    OnlineCount = g.Count(d => d.status == 0),
                    OfflineCount = g.Count(d => d.status == 1),
                    LowBatteryCount = g.Count(d => d.battery_level.HasValue && d.battery_level < 20),
                    WeakSignalCount = g.Count(d => d.signal_strength.HasValue && d.signal_strength < -80)
                })
                .ToList();

            // 电量分布统计
            var batteryStats = new BatteryDistributionStatistics
            {
                Battery0To20 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level >= 0 && d.battery_level <= 20),
                Battery21To50 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 20 && d.battery_level <= 50),
                Battery51To80 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 50 && d.battery_level <= 80),
                Battery81To100 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 80 && d.battery_level <= 100),
                AverageBattery = allDevices.Where(d => d.battery_level.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.battery_level.HasValue).Average(d => d.battery_level.Value), 2)
                    : 0
            };

            // 信号强度分布统计
            var signalStats = new SignalDistributionStatistics
            {
                SignalBelow80 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80),
                Signal80To60 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -80 && d.signal_strength < -60),
                Signal60To40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -60 && d.signal_strength < -40),
                SignalAbove40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -40),
                AverageSignal = allDevices.Where(d => d.signal_strength.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.signal_strength.HasValue).Average(d => d.signal_strength.Value), 2)
                    : 0
            };

            var result = new DeviceDetailedStatisticsOutput
            {
                BasicStatistics = basicStats,
                DeviceTypeStatistics = deviceTypeStats,
                BatteryDistribution = batteryStats,
                SignalDistribution = signalStats
            };

            _logger.LogInformation("条件设备统计查询成功，查询条件：设备类型={DeviceType}，网关ID={ApId}，时间范围={StartTime}-{EndTime}，结果：总设备数={TotalDevices}",
                input.DeviceType, input.ApId, input.StartTime, input.EndTime, totalDevices);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件获取设备统计信息时发生错误，查询条件：{@Input}", input);
            throw Oops.Oh("获取设备统计信息失败");
        }
    }

    /// <summary>
    /// 按会议室分组获取设备统计信息 📊
    /// </summary>
    /// <param name="input">统计查询条件</param>
    /// <returns></returns>
    [DisplayName("按会议室分组获取设备统计信息")]
    [ApiDescriptionSettings(Name = "StatisticsByMeetingRoom"), HttpPost]
    public async Task<List<MeetingRoomDeviceStatistics>> GetDeviceStatisticsByMeetingRoom(DeviceStatisticsInput input)
    {
        try
        {
            var result = new List<MeetingRoomDeviceStatistics>();

            if (input.MeetingRoomId.HasValue && input.MeetingRoomId.Value == 0)
            {
                // 查询所有会议室的统计
                var meetingRooms = await _meetingRoomsRep.AsQueryable()
                    .Where(mr => !mr.IsDelete)
                    .ToListAsync();

                // 添加"所有会议室"的统计
                var allRoomStats = await GetMeetingRoomStatistics(null, input);
                allRoomStats.MeetingRoomId = 0;
                allRoomStats.MeetingRoomName = "所有会议室";
                result.Add(allRoomStats);

                // 添加每个具体会议室的统计
                foreach (var room in meetingRooms)
                {
                    var roomStats = await GetMeetingRoomStatistics(room.Id, input);
                    roomStats.MeetingRoomId = room.Id;
                    roomStats.MeetingRoomName = room.room_name ?? $"会议室{room.Id}";
                    result.Add(roomStats);
                }
            }
            else if (input.MeetingRoomId.HasValue)
            {
                // 查询指定会议室的统计
                var room = await _meetingRoomsRep.GetFirstAsync(mr => mr.Id == input.MeetingRoomId.Value && !mr.IsDelete);
                if (room != null)
                {
                    var roomStats = await GetMeetingRoomStatistics(room.Id, input);
                    roomStats.MeetingRoomId = room.Id;
                    roomStats.MeetingRoomName = room.room_name ?? $"会议室{room.Id}";
                    result.Add(roomStats);
                }
            }
            else
            {
                // 默认查询所有会议室
                var allRoomStats = await GetMeetingRoomStatistics(null, input);
                allRoomStats.MeetingRoomId = 0;
                allRoomStats.MeetingRoomName = "所有会议室";
                result.Add(allRoomStats);
            }

            _logger.LogInformation("按会议室分组设备统计查询成功，会议室数量：{Count}", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按会议室分组获取设备统计信息时发生错误，查询条件：{@Input}", input);
            throw Oops.Oh("获取会议室设备统计信息失败");
        }
    }

    /// <summary>
    /// 获取指定会议室的设备统计信息
    /// </summary>
    /// <param name="meetingRoomId">会议室ID，null表示所有会议室</param>
    /// <param name="input">查询条件</param>
    /// <returns></returns>
    private async Task<MeetingRoomDeviceStatistics> GetMeetingRoomStatistics(long? meetingRoomId, DeviceStatisticsInput input)
    {
        // 构建设备查询条件
        var deviceQuery = _devicesRep.AsQueryable()
            .Where(d => !d.IsDelete);

        // 按时间范围过滤
        if (input.StartTime.HasValue)
        {
            deviceQuery = deviceQuery.Where(d => d.CreateTime >= input.StartTime.Value);
        }

        if (input.EndTime.HasValue)
        {
            deviceQuery = deviceQuery.Where(d => d.CreateTime <= input.EndTime.Value);
        }

        // 按设备类型过滤
        if (input.DeviceType.HasValue)
        {
            deviceQuery = deviceQuery.Where(d => d.device_type == input.DeviceType.Value);
        }

        // 按网关ID过滤
        if (input.ApId.HasValue)
        {
            deviceQuery = deviceQuery.Where(d => d.ap_id == input.ApId.Value);
        }

        List<Devices> devices;

        if (meetingRoomId.HasValue)
        {
            // 查询指定会议室绑定的设备
            devices = await deviceQuery
                .LeftJoin<DeviceBindings>((d, db) => d.Id == db.device_id && !db.IsDelete)
                .Where((d, db) => db.meeting_room_id == meetingRoomId.Value && db.binding_type == 2) // binding_type = 2 表示房间绑定
                .Select((d, db) => d)
                .ToListAsync();
        }
        else
        {
            // 查询所有设备
            devices = await deviceQuery.ToListAsync();
        }

        var totalDevices = devices.Count;
        var onlineDevices = devices.Count(d => d.status == 0);
        var offlineDevices = devices.Count(d => d.status == 1);
        var lowBatteryDevices = devices.Count(d => d.battery_level.HasValue && d.battery_level < 20);
        var weakSignalDevices = devices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80);

        return new MeetingRoomDeviceStatistics
        {
            TotalDevices = totalDevices,
            OnlineDevices = onlineDevices,
            OfflineDevices = offlineDevices,
            LowBatteryDevices = lowBatteryDevices,
            WeakSignalDevices = weakSignalDevices,
            OnlineRate = totalDevices > 0 ? Math.Round((decimal)onlineDevices / totalDevices * 100, 2) : 0
        };
    }

    #endregion
}
